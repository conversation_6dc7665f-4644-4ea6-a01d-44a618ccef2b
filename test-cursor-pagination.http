### Test Cursor-Based Pagination for ProductsCatalog

### 1. Test first page (no cursor)
GET https://localhost:7001/api/ProductsCatalog/CursorPaginated?pageSize=5&direction=Forward
Content-Type: application/json

### 2. Test with invalid cursor (should return validation error)
GET https://localhost:7001/api/ProductsCatalog/CursorPaginated?cursor=invalid-cursor&pageSize=5
Content-Type: application/json

### 3. Test with invalid page size (should return validation error)
GET https://localhost:7001/api/ProductsCatalog/CursorPaginated?pageSize=0
Content-Type: application/json

### 4. Test with invalid direction (should return validation error)
GET https://localhost:7001/api/ProductsCatalog/CursorPaginated?direction=Invalid
Content-Type: application/json

### 5. Test backward pagination
GET https://localhost:7001/api/ProductsCatalog/CursorPaginated?pageSize=5&direction=Backward
Content-Type: application/json

### 6. Compare with original offset-based pagination
GET https://localhost:7001/api/ProductsCatalog/Paginated?pageNumber=1&pageSize=5
Content-Type: application/json
