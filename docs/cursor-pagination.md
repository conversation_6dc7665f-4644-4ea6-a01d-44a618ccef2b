# Cursor-Based Pagination Implementation

## Overview

This document describes the cursor-based pagination implementation for the ProductsCatalog endpoints in ColmAppInventariosApi.

## New Endpoint

### GET /api/ProductsCatalog/CursorPaginated

Retrieves a paginated list of products using cursor-based pagination for better performance with large datasets.

#### Request Parameters

- `cursor` (string, optional): Base64-encoded cursor token for pagination position
- `pageSize` (int, optional): Number of items per page (1-100, default: 10)
- `direction` (enum, optional): Pagination direction (`Forward` or `Backward`, default: `Forward`)

#### Example Requests

**First page (no cursor):**
```
GET /api/ProductsCatalog/CursorPaginated?pageSize=10&direction=Forward
```

**Next page (using cursor from previous response):**
```
GET /api/ProductsCatalog/CursorPaginated?cursor=***********************************************************************************************************************%3D&pageSize=10&direction=Forward
```

**Previous page (backward pagination):**
```
GET /api/ProductsCatalog/CursorPaginated?cursor=***********************************************************************************************************************%3D&pageSize=10&direction=Backward
```

#### Response Format

```json
{
  "isSuccess": true,
  "value": {
    "items": [
      {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "name": "Coca Cola 2L",
        "description": "Bebida gaseosa Coca Cola de 2 litros",
        "barcode": 123456789,
        "shortCode": 1001,
        "measureUnit": "Unidad",
        "createdDate": "2024-01-15T10:30:45.123456Z",
        "updatedDate": null,
        "createdBy": "<EMAIL>",
        "cursorToken": "***********************************************************************************************************************="
      }
    ],
    "pageSize": 10,
    "nextCursor": "***********************************************************************************************************************=",
    "previousCursor": null,
    "hasNextPage": true,
    "hasPreviousPage": false,
    "itemCount": 10
  }
}
```

## Implementation Details

### Cursor Token Structure

Cursor tokens are Base64-encoded JSON objects containing:
- `createdDate`: ISO 8601 timestamp with microsecond precision
- `id`: GUID for tie-breaking when timestamps are identical

### Ordering Strategy

- **Primary sort**: `CreatedDate` (descending for forward, ascending for backward)
- **Secondary sort**: `Id` (descending for forward, ascending for backward)
- **Filtering**: Excludes soft-deleted records (`Deleted = false`)

### Performance Optimizations

The implementation uses efficient database queries with proper indexing:

```sql
-- Recommended indexes for optimal performance
CREATE INDEX IX_products_catalog_cursor_pagination 
ON products_catalog (created_at DESC, id DESC) 
WHERE deleted = false;

CREATE INDEX IX_products_catalog_cursor_pagination_asc 
ON products_catalog (created_at ASC, id ASC) 
WHERE deleted = false;
```

## Backward Compatibility

The original offset-based pagination endpoint remains available:
- **Original**: `GET /api/ProductsCatalog/Paginated` (offset-based)
- **New**: `GET /api/ProductsCatalog/CursorPaginated` (cursor-based)

## Benefits of Cursor Pagination

1. **Consistent Results**: No duplicate or missing items when data changes during pagination
2. **Better Performance**: O(log n) complexity vs O(n) for large offsets
3. **Real-time Friendly**: Works well with frequently updated datasets
4. **Scalable**: Performance doesn't degrade with large datasets

## Error Handling

- **Invalid cursor**: Returns 422 Unprocessable Entity with validation error
- **Invalid pageSize**: Returns 422 Unprocessable Entity (must be 1-100)
- **Invalid direction**: Returns 422 Unprocessable Entity (must be Forward or Backward)

## Migration Guide

To migrate from offset-based to cursor-based pagination:

1. Replace `pageNumber` parameter with `cursor`
2. Add `direction` parameter if backward pagination is needed
3. Use `nextCursor` and `previousCursor` from responses for navigation
4. Handle `hasNextPage` and `hasPreviousPage` for UI state management
