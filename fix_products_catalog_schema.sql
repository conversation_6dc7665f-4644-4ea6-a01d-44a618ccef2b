-- Fix products_catalog table by adding missing audit columns
-- This script adds the columns that the Entity Framework configuration expects

-- Add the missing audit columns to products_catalog table
ALTER TABLE products_catalog 
ADD COLUMN created_by text,
ADD COLUMN updated_by text,
ADD COLUMN deleted_by text,
ADD COLUMN deleted boolean DEFAULT false,
ADD COLUMN deleted_date timestamp with time zone;

-- Verify the updated table structure
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'products_catalog' 
ORDER BY ordinal_position;

-- Optional: Update existing records to set deleted = false if needed
-- (This should already be handled by the DEFAULT false, but just to be safe)
UPDATE products_catalog SET deleted = false WHERE deleted IS NULL;
