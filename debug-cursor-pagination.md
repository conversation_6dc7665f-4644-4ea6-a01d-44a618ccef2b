# Cursor Pagination Debugging Guide

## 🐛 **Issue Identified and Fixed**

The primary issue was in the cursor filtering logic in `CursorPaginationSpecification<T>`. The filtering conditions were inverted relative to the ordering direction.

### **What Was Wrong:**
```csharp
// INCORRECT - Was looking for newer records when we wanted older ones
Query.Where(x => 
  x.CreatedDate > cursor.CreatedDate || 
  (x.CreatedDate == cursor.CreatedDate && x.Id.CompareTo(cursor.Id) > 0));
```

### **What's Fixed:**
```csharp
// CORRECT - Now looking for older records (next page in DESC order)
Query.Where(x => 
  x.CreatedDate < cursor.CreatedDate || 
  (x.CreatedDate == cursor.CreatedDate && x.Id.CompareTo(cursor.Id) < 0));
```

## 🔍 **Debugging Steps to Verify the Fix**

### 1. **Check Database Data**
First, verify you have enough test data:

```sql
-- Check total count and date distribution
SELECT COUNT(*) as total_count, 
       <PERSON><PERSON>(created_at) as oldest, 
       MA<PERSON>(created_at) as newest
FROM products_catalog 
WHERE deleted = false;

-- Check first 10 records in the expected order
SELECT id, name, created_at, deleted
FROM products_catalog 
WHERE deleted = false
ORDER BY created_at DESC, id DESC
LIMIT 10;
```

### 2. **Test the Fixed Implementation**

#### **Step 1: First Request (No Cursor)**
```http
GET /api/ProductsCatalog/CursorPaginated?pageSize=3&direction=Forward
```

**Expected Response Structure:**
```json
{
  "isSuccess": true,
  "value": {
    "items": [
      {
        "id": "newest-record-id",
        "name": "Product 1",
        "createdDate": "2024-01-20T15:30:00Z",
        "cursorToken": "eyJ..."
      },
      {
        "id": "second-newest-id", 
        "name": "Product 2",
        "createdDate": "2024-01-20T14:30:00Z",
        "cursorToken": "eyJ..."
      },
      {
        "id": "third-newest-id",
        "name": "Product 3", 
        "createdDate": "2024-01-20T13:30:00Z",
        "cursorToken": "eyJ..."
      }
    ],
    "nextCursor": "eyJ...", // Cursor for the last item
    "hasNextPage": true,
    "itemCount": 3
  }
}
```

#### **Step 2: Second Request (Using nextCursor)**
```http
GET /api/ProductsCatalog/CursorPaginated?cursor={nextCursor}&pageSize=3&direction=Forward
```

**Expected Behavior:**
- Should return the NEXT 3 oldest records
- Should NOT include any records from the first response
- CreatedDate values should be older than the last item from step 1

### 3. **Debug Cursor Token**

Add this debugging code to your handler to inspect cursor tokens:

```csharp
// In ListCursorPaginatedProductsCatalogHandler.cs
public async Task<Result<CursorPaginationResult<ProductsCatalogCursorResponseDto>>> Handle(
    ListCursorPaginatedProductsCatalogQuery request, 
    CancellationToken cancellationToken)
{
    // DEBUG: Log cursor details
    if (!string.IsNullOrEmpty(request.Cursor))
    {
        var decodedCursor = CursorToken.Decode(request.Cursor);
        await _logService.LogInfoAsync($"DEBUG - Decoded cursor: CreatedDate={decodedCursor?.CreatedDate}, Id={decodedCursor?.Id}");
    }
    
    // ... rest of your existing code
    
    // DEBUG: Log first and last items
    if (pagedResult.Items.Any())
    {
        var first = pagedResult.Items.First();
        var last = pagedResult.Items.Last();
        await _logService.LogInfoAsync($"DEBUG - First item: CreatedDate={first.CreatedDate}, Id={first.Id}");
        await _logService.LogInfoAsync($"DEBUG - Last item: CreatedDate={last.CreatedDate}, Id={last.Id}");
        await _logService.LogInfoAsync($"DEBUG - NextCursor: {pagedResult.NextCursor}");
    }
}
```

### 4. **Verify Cursor Token Encoding/Decoding**

Test cursor token functionality:

```csharp
// Test in a unit test or console app
var testDate = DateTimeOffset.UtcNow;
var testId = Guid.NewGuid();
var originalToken = new CursorToken(testDate, testId);

var encoded = originalToken.Encode();
Console.WriteLine($"Encoded: {encoded}");

var decoded = CursorToken.Decode(encoded);
Console.WriteLine($"Decoded - Date: {decoded?.CreatedDate}, Id: {decoded?.Id}");

// Verify they match
Assert.Equal(originalToken.CreatedDate, decoded?.CreatedDate);
Assert.Equal(originalToken.Id, decoded?.Id);
```

### 5. **SQL Query Verification**

The generated SQL should look like this for forward pagination with cursor:

```sql
SELECT p.id, p.name, p.created_at, p.deleted, ...
FROM products_catalog p
WHERE p.deleted = false 
  AND (p.created_at < @cursor_created_date 
       OR (p.created_at = @cursor_created_date AND p.id < @cursor_id))
ORDER BY p.created_at DESC, p.id DESC
LIMIT 4; -- pageSize + 1
```

### 6. **Common Issues to Check**

1. **Database Timezone Issues**: Ensure CreatedDate is stored consistently
2. **GUID Comparison**: Verify GUID comparison works correctly in your database
3. **Soft Delete**: Ensure `deleted = false` filter is applied
4. **Index Usage**: Verify the database is using the correct indexes

### 7. **Test Scenarios**

Create these test scenarios:

1. **Empty Database**: Should return empty results gracefully
2. **Single Page**: When total records ≤ pageSize, hasNextPage should be false
3. **Exact Page Boundary**: When total records = pageSize * n
4. **Duplicate Timestamps**: Multiple records with same CreatedDate
5. **Backward Pagination**: Test going backwards through pages

## 🧪 **Quick Verification Script**

Run this test to verify the fix works:

```bash
# Build and test
dotnet build
dotnet test

# Start the application
dotnet run --project src/ColmAppInventariosApi.Web

# Test the endpoints (in another terminal)
curl -X GET "https://localhost:7001/api/ProductsCatalog/CursorPaginated?pageSize=2" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

The fix should resolve the issue where the same records were being returned. The cursor filtering now correctly identifies records that come after the cursor position in the ordered sequence.
