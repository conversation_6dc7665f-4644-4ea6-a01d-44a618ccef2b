using Ardalis.Result;
using FastEndpoints;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace ColmAppInventariosApi.Web.Endpoints.HealthChecks;

[AllowAnonymous]
public class HealthCheck : EndpointWithoutRequest<Result<HealthCheckResponse>>
{
  private readonly HealthCheckService _healthCheckService;
  public HealthCheck(HealthCheckService healthCheckService)
  {
    _healthCheckService = healthCheckService;
  }
  public override void Configure()
  {
    Get("Health/Check");
    Summary(s =>
    {
      s.Summary = "Obtiene el estado de salud de la API y sus dependencias";
      s.Description = "Devuelve información detallada sobre la salud de la API y sus servicios conectados";
      s.ResponseExamples[200] = new HealthCheckResponse
      {
        Status = "Healthy",
        Components = new List<HealthCheckComponentResponse>
        {
          new() { Name = "API", Status = "Healthy" },
          new() { Name = "Database", Status = "Healthy" }
        }
      };
    });
    AllowAnonymous();
  }
  public override async Task HandleAsync(CancellationToken cancellationToken)
  {
    var healthReport = await _healthCheckService.CheckHealthAsync(cancellationToken);
    var response = new HealthCheckResponse
    {
      Status = healthReport.Status.ToString(),
      Components = healthReport.Entries.Select(entry => new HealthCheckComponentResponse
      {
        Name = entry.Key,
        Status = entry.Value.Status.ToString(),
        Description = entry.Value.Description,
        Error = entry.Value.Exception?.Message
      }).ToList()
    };
    Response = Result<HealthCheckResponse>.Success(response);
  }
}
