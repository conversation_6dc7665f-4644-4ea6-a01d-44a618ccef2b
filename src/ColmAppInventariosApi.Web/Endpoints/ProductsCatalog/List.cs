using Ardalis.Result;
using ColmAppInventariosApi.UseCases.ProductsCatalog.Dtos;
using ColmAppInventariosApi.UseCases.ProductsCatalog.Queries.List;
using FastEndpoints;
using MediatR;
using Microsoft.AspNetCore.Authorization;

namespace ColmAppInventariosApi.Web.Endpoints.ProductsCatalog;

public class List(IMediator mediator) : EndpointWithoutRequest<IEnumerable<ProductsCatalogResponseDto>>
{
  public override void Configure()
  {
    Get("ProductsCatalog");
    Summary(s =>
    {
      s.Summary = "Obtiene lista de todos los productos del catálogo";
    });
    AllowAnonymous();
  }

  public override async Task HandleAsync(CancellationToken cancellationToken)
  {
    var result = await mediator.Send(new ListProductsCatalogQuery(), cancellationToken);

    switch (result.Status)
    {
      case ResultStatus.Ok:
        Response = result.Value;
        break;
      default:
        ThrowError(result.Errors.First().ToString());
        break;
    }
  }
}
