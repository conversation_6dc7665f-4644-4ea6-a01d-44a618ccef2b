using Ardalis.Result;
using ColmAppInventariosApi.UseCases.ProductsCatalog.Dtos;
using ColmAppInventariosApi.UseCases.ProductsCatalog.Queries.GetById;
using FastEndpoints;
using MediatR;
using Microsoft.AspNetCore.Authorization;

namespace ColmAppInventariosApi.Web.Endpoints.ProductsCatalog;

public class GetById(IMediator mediator) : EndpointWithoutRequest<Result<ProductsCatalogResponseDto>>
{
  public override void Configure()
  {
    Get("ProductsCatalog/{productsCatalogId:Guid}");
    Summary(s =>
    {
      s.Summary = "Obtiene un producto del catálogo por ID";
    });
    AllowAnonymous();
  }

  public override async Task HandleAsync(CancellationToken cancellationToken)
  {
    var productsCatalogId = Route<Guid>("productsCatalogId");
    var result = await mediator.Send(new GetByIdProductsCatalogQuery(productsCatalogId), cancellationToken);

    switch (result.Status)
    {
      case ResultStatus.Ok:
        Response = result;
        break;
      case ResultStatus.NotFound:
        ThrowError("Producto no encontrado");
        break;
      default:
        ThrowError(result.Errors.First().ToString());
        break;
    }
  }
}
