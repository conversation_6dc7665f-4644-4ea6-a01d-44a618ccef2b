using ColmAppInventariosApi.UseCases.ProductsCatalog.Commands.Update;
using ColmAppInventariosApi.UseCases.ProductsCatalog.Dtos;
using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Authorization;

namespace ColmAppInventariosApi.Web.Endpoints.ProductsCatalog;

public class Update : BaseEndpoint<UpdateProductsCatalogDto, ProductsCatalogResponseDto>
{
  public Update(IMediator mediator, IValidator<UpdateProductsCatalogDto> validator)
      : base(mediator, validator)
  {
  }

  public override void Configure()
  {
    Put("ProductsCatalog");
    Summary(s =>
    {
      s.Summary = "Actualiza un producto existente en el catálogo";
      s.ExampleRequest = new UpdateProductsCatalogDto()
      {
        ProductsCatalogId = Guid.NewGuid(),
        Name = "Coca Cola 2L",
        Description = "Bebida gaseosa Coca Cola de 2 litros",
        Barcode = 123456789,
        ShortCode = 1001,
        MeasureUnit = "Unidad"
      };
    });
    AllowAnonymous();
  }

  protected override async Task HandleValidRequestAsync(
      UpdateProductsCatalogDto request,
      CancellationToken cancellationToken)
  {
    await SendCommandAsync(new UpdateProductsCatalogCommand(request), cancellationToken);
  }
}
