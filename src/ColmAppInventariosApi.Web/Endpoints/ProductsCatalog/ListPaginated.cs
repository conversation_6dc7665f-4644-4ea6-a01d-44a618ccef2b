using Ardalis.Result;
using ColmAppInventariosApi.Core.Abstract;
using ColmAppInventariosApi.UseCases.ProductsCatalog.Dtos;
using ColmAppInventariosApi.UseCases.ProductsCatalog.Queries.ListPaginated;
using FastEndpoints;
using MediatR;
using Microsoft.AspNetCore.Authorization;

namespace ColmAppInventariosApi.Web.Endpoints.ProductsCatalog;

public class ListPaginatedProductsCatalogRequest
{
  public int PageNumber { get; set; } = 1;
  public int PageSize { get; set; } = 10;
}

public class ListPaginated(IMediator mediator)
    : Endpoint<ListPaginatedProductsCatalogRequest, PaginationResult<ProductsCatalogResponseDto>>
{
  public override void Configure()
  {
    Get("ProductsCatalog/Paginated");
    Summary(s =>
    {
      s.Summary = "Obtiene lista paginada de productos del catálogo";
      s.Description = "Recupera una lista paginada de productos del catálogo con parámetros de paginación";
      s.ExampleRequest = new ListPaginatedProductsCatalogRequest()
      {
        PageNumber = 1,
        PageSize = 10
      };
    });
    AllowAnonymous();
  }

  public override async Task HandleAsync(ListPaginatedProductsCatalogRequest req, CancellationToken ct)
  {
    var result = await mediator.Send(new ListPaginatedProductsCatalogQuery(req.PageNumber, req.PageSize), ct);

    switch (result.Status)
    {
      case ResultStatus.Ok:
        Response = result.Value;
        break;
      default:
        ThrowError(result.Errors.First().ToString());
        break;
    }
  }
}
