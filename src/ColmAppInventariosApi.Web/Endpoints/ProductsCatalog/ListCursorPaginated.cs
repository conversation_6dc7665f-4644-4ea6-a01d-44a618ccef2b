using Ardalis.Result;
using ColmAppInventariosApi.Core.Abstract;
using ColmAppInventariosApi.UseCases.ProductsCatalog.Dtos;
using ColmAppInventariosApi.UseCases.ProductsCatalog.Queries.ListCursorPaginated;
using ColmAppInventariosApi.UseCases.ProductsCatalog.Validators;
using FastEndpoints;
using MediatR;
using Microsoft.AspNetCore.Authorization;

namespace ColmAppInventariosApi.Web.Endpoints.ProductsCatalog;

public class ListCursorPaginated(IMediator mediator)
    : Endpoint<ListCursorPaginatedProductsCatalogRequest, Result<CursorPaginationResult<ProductsCatalogCursorResponseDto>>>
{
  public override void Configure()
  {
    Get("ProductsCatalog/CursorPaginated");
    Summary(s =>
    {
      s.Summary = "Obtiene lista paginada de productos del catálogo usando cursor";
      s.Description = "Recupera una lista paginada usando cursor-based pagination para mejor rendimiento en datasets grandes";
      s.ExampleRequest = new ListCursorPaginatedProductsCatalogRequest()
      {
        Cursor = null,
        PageSize = 10,
        Direction = CursorDirection.Forward
      };
    });
    AllowAnonymous();
  }

  public override async Task HandleAsync(ListCursorPaginatedProductsCatalogRequest req, CancellationToken ct)
  {
    var result = await mediator.Send(
      new ListCursorPaginatedProductsCatalogQuery(req.Cursor, req.PageSize, req.Direction), 
      ct);

    switch (result.Status)
    {
      case ResultStatus.Ok:
        Response = result;
        break;
      default:
        ThrowError(result.Errors.First().ToString());
        break;
    }
  }
}
