using Ardalis.Result;
using Ardalis.SharedKernel;
using AutoMapper;
using ColmAppInventariosApi.Core.Abstract;
using ColmAppInventariosApi.Core.Aggregates.AbstractAggregate.Specs;

namespace ColmAppInventariosApi.UseCases.Commons.Paginations;

/// <summary>
/// Cursor-based pagination extensions for repositories
/// </summary>
public static class CursorPaginationExtensions
{
  /// <summary>
  /// Gets a cursor-based paginated list of entities mapped to DTOs
  /// </summary>
  public static async Task<CursorPaginationResult<TDto>> GetCursorPagedAsync<TEntity, TDto>(
    this IReadRepository<TEntity> repository,
    string? cursor,
    int pageSize,
    CursorDirection direction,
    IMapper mapper,
    CancellationToken cancellationToken = default) 
    where TEntity : BaseEntity, IAggregateRoot
  {
    // Decode cursor token
    var cursorToken = CursorToken.Decode(cursor);

    // Create cursor pagination specification
    var spec = new CursorPaginationSpecification<TEntity>(cursorToken, pageSize, direction);

    // Get items using the specification
    var items = await repository.ListAsync(spec, cancellationToken);

    // Determine if there are more pages
    var hasMoreItems = items.Count > pageSize;
    var actualItems = hasMoreItems ? items.Take(pageSize).ToList() : items.ToList();

    // For backward pagination, reverse the results to maintain chronological order
    if (direction == CursorDirection.Backward)
    {
      actualItems.Reverse();
    }

    // Map entities to DTOs
    var mappedItems = mapper.Map<List<TDto>>(actualItems);

    // Generate cursor tokens for navigation
    string? nextCursor = null;
    string? previousCursor = null;

    if (actualItems.Any())
    {
      var firstItem = actualItems.First();
      var lastItem = actualItems.Last();

      if (direction == CursorDirection.Forward)
      {
        // For forward pagination
        previousCursor = CursorToken.FromEntity(firstItem).Encode();
        if (hasMoreItems)
        {
          nextCursor = CursorToken.FromEntity(lastItem).Encode();
        }
      }
      else
      {
        // For backward pagination
        nextCursor = CursorToken.FromEntity(lastItem).Encode();
        if (hasMoreItems)
        {
          previousCursor = CursorToken.FromEntity(firstItem).Encode();
        }
      }
    }

    // Determine pagination state
    var hasNextPage = direction == CursorDirection.Forward ? hasMoreItems : cursorToken != null;
    var hasPreviousPage = direction == CursorDirection.Backward ? hasMoreItems : cursorToken != null;

    return new CursorPaginationResult<TDto>(
      mappedItems,
      pageSize,
      nextCursor,
      previousCursor,
      hasNextPage,
      hasPreviousPage);
  }

  /// <summary>
  /// Simplifies creation of a successful cursor-based paginated result
  /// </summary>
  public static Result<CursorPaginationResult<T>> ToSuccessResult<T>(this CursorPaginationResult<T> pagedResult)
  {
    return Result.Success(pagedResult);
  }
}
