using ColmAppInventariosApi.Core.Abstract;
using FluentValidation;

namespace ColmAppInventariosApi.UseCases.ProductsCatalog.Validators;

public class ListCursorPaginatedProductsCatalogRequestValidator : AbstractValidator<ListCursorPaginatedProductsCatalogRequest>
{
  public ListCursorPaginatedProductsCatalogRequestValidator()
  {
    RuleFor(x => x.PageSize)
      .GreaterThan(0).WithMessage("PageSize debe ser mayor que 0")
      .LessThanOrEqualTo(100).WithMessage("PageSize no puede ser mayor que 100");

    RuleFor(x => x.Cursor)
      .Must(BeValidCursorToken).WithMessage("Cursor token inválido")
      .When(x => !string.IsNullOrEmpty(x.Cursor));

    RuleFor(x => x.Direction)
      .IsInEnum().WithMessage("Direction debe ser Forward o Backward");
  }

  private bool BeValidCursorToken(string? cursor)
  {
    if (string.IsNullOrWhiteSpace(cursor))
      return true;

    return CursorToken.Decode(cursor) != null;
  }
}

/// <summary>
/// Request model for cursor-based pagination of ProductsCatalog
/// </summary>
public class ListCursorPaginatedProductsCatalogRequest
{
  public string? Cursor { get; set; }
  public int PageSize { get; set; } = 10;
  public CursorDirection Direction { get; set; } = CursorDirection.Forward;
}
