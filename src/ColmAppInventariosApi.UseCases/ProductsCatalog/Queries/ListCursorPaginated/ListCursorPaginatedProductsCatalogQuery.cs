using Ardalis.Result;
using Ardalis.SharedKernel;
using ColmAppInventariosApi.Core.Abstract;
using ColmAppInventariosApi.UseCases.ProductsCatalog.Dtos;

namespace ColmAppInventariosApi.UseCases.ProductsCatalog.Queries.ListCursorPaginated;

public record ListCursorPaginatedProductsCatalogQuery(
  string? Cursor = null, 
  int PageSize = 10, 
  CursorDirection Direction = CursorDirection.Forward
) : IQuery<Result<CursorPaginationResult<ProductsCatalogCursorResponseDto>>>;
