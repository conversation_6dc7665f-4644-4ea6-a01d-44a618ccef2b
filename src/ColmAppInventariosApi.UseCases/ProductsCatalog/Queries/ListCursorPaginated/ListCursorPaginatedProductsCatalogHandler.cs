using Ardalis.Result;
using Ardalis.SharedKernel;
using AutoMapper;
using ColmAppInventariosApi.Core.Abstract;
using ColmAppInventariosApi.Core.Aggregates.ProductsCatalogAggregate;
using ColmAppInventariosApi.Core.Interfaces;
using ColmAppInventariosApi.UseCases.ProductsCatalog.Dtos;
using ColmAppInventariosApi.UseCases.Commons.Paginations;

namespace ColmAppInventariosApi.UseCases.ProductsCatalog.Queries.ListCursorPaginated;

public class ListCursorPaginatedProductsCatalogHandler 
  : IQueryHandler<ListCursorPaginatedProductsCatalogQuery, Result<CursorPaginationResult<ProductsCatalogCursorResponseDto>>>
{
  private readonly IReadRepository<Core.Aggregates.ProductsCatalogAggregate.ProductsCatalog> _productsCatalogReadRepository;
  private readonly IMapper _mapper;
  private readonly ILogService _logService;

  public ListCursorPaginatedProductsCatalogHandler(
    IReadRepository<Core.Aggregates.ProductsCatalogAggregate.ProductsCatalog> productsCatalogReadRepository,
    IMapper mapper,
    ILogService logService)
  {
    _productsCatalogReadRepository = productsCatalogReadRepository;
    _mapper = mapper;
    _logService = logService;
  }

  public async Task<Result<CursorPaginationResult<ProductsCatalogCursorResponseDto>>> Handle(
    ListCursorPaginatedProductsCatalogQuery request, 
    CancellationToken cancellationToken)
  {
    await _logService.LogInfoAsync($"Recuperando productos del catálogo con cursor pagination " +
                                 $"(Cursor: {request.Cursor ?? "null"}, Tamaño: {request.PageSize}, " +
                                 $"Dirección: {request.Direction})");

    try
    {
      var pagedResult = await _productsCatalogReadRepository.GetCursorPagedAsync<Core.Aggregates.ProductsCatalogAggregate.ProductsCatalog, ProductsCatalogCursorResponseDto>(
        request.Cursor,
        request.PageSize,
        request.Direction,
        _mapper,
        cancellationToken);

      // Add cursor tokens to each item for convenience
      foreach (var item in pagedResult.Items)
      {
        var cursorToken = new CursorToken(item.CreatedDate, item.Id);
        item.CursorToken = cursorToken.Encode();
      }

      await _logService.LogSuccessAsync($"Productos del catálogo con cursor pagination recuperados exitosamente " +
                                       $"(Cursor: {request.Cursor ?? "null"}, Tamaño: {request.PageSize}, " +
                                       $"Dirección: {request.Direction}, Items: {pagedResult.ItemCount}, " +
                                       $"HasNext: {pagedResult.HasNextPage}, HasPrevious: {pagedResult.HasPreviousPage})");

      return pagedResult.ToSuccessResult();
    }
    catch (Exception ex)
    {
      await _logService.LogErrorAsync($"Error al recuperar productos del catálogo con cursor pagination " +
                                     $"(Cursor: {request.Cursor ?? "null"}, Tamaño: {request.PageSize}, " +
                                     $"Dirección: {request.Direction})", ex);
      return Result.Error(ex.Message);
    }
  }
}
