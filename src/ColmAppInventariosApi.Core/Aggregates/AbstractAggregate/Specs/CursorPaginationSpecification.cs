using Ardalis.SharedKernel;
using Ardalis.Specification;
using ColmAppInventariosApi.Core.Abstract;

namespace ColmAppInventariosApi.Core.Aggregates.AbstractAggregate.Specs;

/// <summary>
/// A specification that applies cursor-based pagination constraints
/// Uses CreatedDate and Id for stable ordering and cursor-based filtering
/// </summary>
public class CursorPaginationSpecification<T> : Specification<T> where T : BaseEntity, IAggregateRoot
{
  public CursorPaginationSpecification(
    CursorToken? cursor, 
    int pageSize, 
    CursorDirection direction = CursorDirection.Forward)
  {
    // Apply cursor filtering if cursor is provided
    if (cursor != null)
    {
      if (direction == CursorDirection.Forward)
      {
        // For forward pagination with DESC ordering: get records that come AFTER the cursor in the ordered sequence
        // Since we order by CreatedDate DESC, Id DESC, "after" means smaller values
        // (CreatedDate < cursor.CreatedDate) OR (CreatedDate = cursor.CreatedDate AND Id < cursor.Id)
        Query.Where(x =>
          x.CreatedDate < cursor.CreatedDate ||
          (x.CreatedDate == cursor.CreatedDate && x.Id.CompareTo(cursor.Id) < 0));
      }
      else
      {
        // For backward pagination with ASC ordering: get records that come BEFORE the cursor in the ordered sequence
        // Since we order by CreatedDate ASC, Id ASC, "before" means larger values (going backwards in time)
        // (CreatedDate > cursor.CreatedDate) OR (CreatedDate = cursor.CreatedDate AND Id > cursor.Id)
        Query.Where(x =>
          x.CreatedDate > cursor.CreatedDate ||
          (x.CreatedDate == cursor.CreatedDate && x.Id.CompareTo(cursor.Id) > 0));
      }
    }

    // Apply ordering based on direction
    if (direction == CursorDirection.Forward)
    {
      // Forward: newest first (descending)
      Query.OrderByDescending(x => x.CreatedDate)
           .ThenByDescending(x => x.Id);
    }
    else
    {
      // Backward: oldest first (ascending) - will be reversed later
      Query.OrderBy(x => x.CreatedDate)
           .ThenBy(x => x.Id);
    }

    // Apply pagination - get one extra item to determine if there are more pages
    Query.Take(pageSize + 1);

    // Exclude deleted items
    Query.Where(x => !x.Deleted);
  }
}
