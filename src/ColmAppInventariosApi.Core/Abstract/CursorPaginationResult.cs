namespace ColmAppInventariosApi.Core.Abstract;

/// <summary>
/// Generic cursor-based paged result containing items and cursor pagination metadata
/// </summary>
public class CursorPaginationResult<T>
{
  public IReadOnlyList<T> Items { get; }
  public int PageSize { get; }
  public string? NextCursor { get; }
  public string? PreviousCursor { get; }
  public bool HasNextPage { get; }
  public bool HasPreviousPage { get; }
  public int ItemCount { get; }

  public CursorPaginationResult(
    IEnumerable<T> items, 
    int pageSize, 
    string? nextCursor = null, 
    string? previousCursor = null,
    bool hasNextPage = false,
    bool hasPreviousPage = false)
  {
    var itemsList = items as IReadOnlyList<T> ?? new List<T>(items).AsReadOnly();

    Items = itemsList;
    PageSize = pageSize;
    NextCursor = nextCursor;
    PreviousCursor = previousCursor;
    HasNextPage = hasNextPage;
    HasPreviousPage = hasPreviousPage;
    ItemCount = itemsList.Count;
  }
}
