namespace ColmAppInventariosApi.Core.Abstract;

/// <summary>
/// Base filter parameters for cursor-based pagination
/// </summary>
public abstract class CursorPaginationFilter
{
  private int _pageSize = 10;

  public string? Cursor { get; set; }

  public int PageSize
  {
    get => _pageSize;
    set => _pageSize = value < 1 ? 10 : (value > 100 ? 100 : value);
  }

  public CursorDirection Direction { get; set; } = CursorDirection.Forward;
}

/// <summary>
/// Direction for cursor-based pagination
/// </summary>
public enum CursorDirection
{
  Forward,
  Backward
}
