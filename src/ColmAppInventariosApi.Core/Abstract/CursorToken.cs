using System.Text;
using System.Text.Json;

namespace ColmAppInventariosApi.Core.Abstract;

/// <summary>
/// Represents a cursor token for pagination containing CreatedDate and Id for stable ordering
/// </summary>
public class CursorToken
{
  public DateTimeOffset CreatedDate { get; set; }
  public Guid Id { get; set; }

  public CursorToken()
  {
  }

  public CursorToken(DateTimeOffset createdDate, Guid id)
  {
    CreatedDate = createdDate;
    Id = id;
  }

  /// <summary>
  /// Encodes the cursor token to a Base64 JSON string
  /// </summary>
  public string Encode()
  {
    try
    {
      var json = JsonSerializer.Serialize(this, new JsonSerializerOptions
      {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
      });
      var bytes = Encoding.UTF8.GetBytes(json);
      return Convert.ToBase64String(bytes);
    }
    catch
    {
      throw new InvalidOperationException("Failed to encode cursor token");
    }
  }

  /// <summary>
  /// Decodes a Base64 JSON string to a cursor token
  /// </summary>
  public static CursorToken? Decode(string? cursor)
  {
    if (string.IsNullOrWhiteSpace(cursor))
      return null;

    try
    {
      var bytes = Convert.FromBase64String(cursor);
      var json = Encoding.UTF8.GetString(bytes);
      return JsonSerializer.Deserialize<CursorToken>(json, new JsonSerializerOptions
      {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
      });
    }
    catch
    {
      return null;
    }
  }

  /// <summary>
  /// Creates a cursor token from a BaseEntity
  /// </summary>
  public static CursorToken FromEntity(BaseEntity entity)
  {
    return new CursorToken(entity.CreatedDate, entity.Id);
  }
}
