﻿using AutoMapper;
using ColmAppInventariosApi.Core.Aggregates.ProductsCatalogAggregate;
using ColmAppInventariosApi.UseCases.ProductsCatalog.Dtos;

namespace ColmAppInventariosApi.Infrastructure.Config;

public class MainMapperProfile : Profile
{
  public MainMapperProfile()
  {
    // Add Mapping profiles here
    CreateMap<ProductsCatalog, ProductsCatalogRequestDto>().ReverseMap();
    CreateMap<ProductsCatalog, UpdateProductsCatalogDto>().ReverseMap();
    CreateMap<ProductsCatalog, ProductsCatalogResponseDto>().ReverseMap();
    CreateMap<ProductsCatalog, ProductsCatalogCursorResponseDto>().ReverseMap();
  }
}
