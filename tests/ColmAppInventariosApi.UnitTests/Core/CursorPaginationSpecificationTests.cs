using Ardalis.SharedKernel;
using ColmAppInventariosApi.Core.Abstract;
using ColmAppInventariosApi.Core.Aggregates.AbstractAggregate.Specs;
using FluentAssertions;
using Xunit;

namespace ColmAppInventariosApi.UnitTests.Core;

public class CursorPaginationSpecificationTests
{
  [Fact]
  public void ForwardPagination_WithCursor_ShouldFilterCorrectly()
  {
    // Arrange
    var cursorDate = new DateTimeOffset(2024, 1, 15, 10, 30, 45, TimeSpan.Zero);
    var cursorId = Guid.Parse("550e8400-e29b-41d4-a716-************");
    var cursor = new CursorToken(cursorDate, cursorId);
    
    // Create test entities
    var smallerId = Guid.Parse("450e8400-e29b-41d4-a716-************"); // Smaller than cursor ID
    var largerId = Guid.Parse("650e8400-e29b-41d4-a716-************");  // Larger than cursor ID

    var entities = new List<TestEntity>
    {
      // These should be EXCLUDED (newer than cursor or same date with larger ID)
      new() { Id = Guid.NewGuid(), CreatedDate = cursorDate.AddMinutes(1), Deleted = false },
      new() { Id = largerId, CreatedDate = cursorDate, Deleted = false }, // Same date but larger ID

      // These should be INCLUDED (older than cursor or same date with smaller ID)
      new() { Id = smallerId, CreatedDate = cursorDate, Deleted = false }, // Same date but smaller ID
      new() { Id = Guid.NewGuid(), CreatedDate = cursorDate.AddMinutes(-1), Deleted = false },
      new() { Id = Guid.NewGuid(), CreatedDate = cursorDate.AddMinutes(-2), Deleted = false },
    };

    // Act
    var spec = new CursorPaginationSpecification<TestEntity>(cursor, 10, CursorDirection.Forward);
    
    // Simulate the filtering logic
    var filteredEntities = entities.Where(x => 
      x.CreatedDate < cursor.CreatedDate || 
      (x.CreatedDate == cursor.CreatedDate && x.Id.CompareTo(cursor.Id) < 0))
      .Where(x => !x.Deleted)
      .OrderByDescending(x => x.CreatedDate)
      .ThenByDescending(x => x.Id)
      .Take(11) // pageSize + 1
      .ToList();

    // Assert
    filteredEntities.Should().HaveCount(3); // Should include 3 items: same date with smaller ID + 2 older items
    filteredEntities.All(x => x.CreatedDate < cursorDate ||
                            (x.CreatedDate == cursorDate && x.Id.CompareTo(cursorId) < 0))
                   .Should().BeTrue();
  }

  [Fact]
  public void BackwardPagination_WithCursor_ShouldFilterCorrectly()
  {
    // Arrange
    var cursorDate = new DateTimeOffset(2024, 1, 15, 10, 30, 45, TimeSpan.Zero);
    var cursorId = Guid.Parse("550e8400-e29b-41d4-a716-************");
    var cursor = new CursorToken(cursorDate, cursorId);
    
    // Create test entities
    var entities = new List<TestEntity>
    {
      // These should be INCLUDED (newer than cursor - going backwards)
      new() { Id = Guid.NewGuid(), CreatedDate = cursorDate.AddMinutes(1), Deleted = false },
      new() { Id = Guid.NewGuid(), CreatedDate = cursorDate.AddMinutes(2), Deleted = false },
      
      // These should be EXCLUDED (older than cursor)
      new() { Id = Guid.NewGuid(), CreatedDate = cursorDate.AddMinutes(-1), Deleted = false },
      new() { Id = Guid.NewGuid(), CreatedDate = cursorDate, Deleted = false }, // Same date but different ID
    };

    // Act
    var spec = new CursorPaginationSpecification<TestEntity>(cursor, 10, CursorDirection.Backward);
    
    // Simulate the filtering logic
    var filteredEntities = entities.Where(x => 
      x.CreatedDate > cursor.CreatedDate || 
      (x.CreatedDate == cursor.CreatedDate && x.Id.CompareTo(cursor.Id) > 0))
      .Where(x => !x.Deleted)
      .OrderBy(x => x.CreatedDate)
      .ThenBy(x => x.Id)
      .Take(11) // pageSize + 1
      .ToList();

    // Assert
    filteredEntities.Should().HaveCount(2);
    filteredEntities.All(x => x.CreatedDate > cursorDate || 
                            (x.CreatedDate == cursorDate && x.Id.CompareTo(cursorId) > 0))
                   .Should().BeTrue();
  }

  [Fact]
  public void NoCursor_ShouldNotApplyFiltering()
  {
    // Arrange
    var entities = new List<TestEntity>
    {
      new() { Id = Guid.NewGuid(), CreatedDate = DateTimeOffset.Now.AddMinutes(-1), Deleted = false },
      new() { Id = Guid.NewGuid(), CreatedDate = DateTimeOffset.Now.AddMinutes(-2), Deleted = false },
      new() { Id = Guid.NewGuid(), CreatedDate = DateTimeOffset.Now.AddMinutes(-3), Deleted = true }, // Should be excluded
    };

    // Act
    var spec = new CursorPaginationSpecification<TestEntity>(null, 10, CursorDirection.Forward);
    
    // Simulate the filtering logic (no cursor filtering, only deleted filtering)
    var filteredEntities = entities.Where(x => !x.Deleted)
      .OrderByDescending(x => x.CreatedDate)
      .ThenByDescending(x => x.Id)
      .Take(11) // pageSize + 1
      .ToList();

    // Assert
    filteredEntities.Should().HaveCount(2); // Deleted item should be excluded
    filteredEntities.All(x => !x.Deleted).Should().BeTrue();
  }

  private class TestEntity : BaseEntity, IAggregateRoot
  {
    // Test entity for testing cursor pagination
  }
}
