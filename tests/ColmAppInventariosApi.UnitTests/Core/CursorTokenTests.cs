using ColmAppInventariosApi.Core.Abstract;
using FluentAssertions;
using Xunit;

namespace ColmAppInventariosApi.UnitTests.Core;

public class CursorTokenTests
{
  [Fact]
  public void Encode_ShouldReturnValidBase64String()
  {
    // Arrange
    var createdDate = new DateTimeOffset(2024, 1, 15, 10, 30, 45, TimeSpan.Zero);
    var id = Guid.NewGuid();
    var cursorToken = new CursorToken(createdDate, id);

    // Act
    var encoded = cursorToken.Encode();

    // Assert
    encoded.Should().NotBeNullOrEmpty();
    encoded.Should().MatchRegex(@"^[A-Za-z0-9+/]*={0,2}$"); // Valid Base64 pattern
  }

  [Fact]
  public void Decode_WithValidToken_ShouldReturnCursorToken()
  {
    // Arrange
    var createdDate = new DateTimeOffset(2024, 1, 15, 10, 30, 45, TimeSpan.Zero);
    var id = Guid.NewGuid();
    var originalToken = new CursorToken(createdDate, id);
    var encoded = originalToken.Encode();

    // Act
    var decoded = CursorToken.Decode(encoded);

    // Assert
    decoded.Should().NotBeNull();
    decoded!.CreatedDate.Should().Be(createdDate);
    decoded.Id.Should().Be(id);
  }

  [Fact]
  public void Decode_WithInvalidToken_ShouldReturnNull()
  {
    // Arrange
    var invalidToken = "invalid-token";

    // Act
    var decoded = CursorToken.Decode(invalidToken);

    // Assert
    decoded.Should().BeNull();
  }

  [Fact]
  public void Decode_WithNullOrEmpty_ShouldReturnNull()
  {
    // Act & Assert
    CursorToken.Decode(null).Should().BeNull();
    CursorToken.Decode("").Should().BeNull();
    CursorToken.Decode("   ").Should().BeNull();
  }

  [Fact]
  public void FromEntity_ShouldCreateValidCursorToken()
  {
    // Arrange
    var entity = new TestEntity
    {
      Id = Guid.NewGuid(),
      CreatedDate = DateTimeOffset.UtcNow
    };

    // Act
    var cursorToken = CursorToken.FromEntity(entity);

    // Assert
    cursorToken.Should().NotBeNull();
    cursorToken.Id.Should().Be(entity.Id);
    cursorToken.CreatedDate.Should().Be(entity.CreatedDate);
  }

  [Fact]
  public void EncodeAndDecode_ShouldBeReversible()
  {
    // Arrange
    var createdDate = DateTimeOffset.UtcNow;
    var id = Guid.NewGuid();
    var originalToken = new CursorToken(createdDate, id);

    // Act
    var encoded = originalToken.Encode();
    var decoded = CursorToken.Decode(encoded);

    // Assert
    decoded.Should().NotBeNull();
    decoded!.CreatedDate.Should().Be(originalToken.CreatedDate);
    decoded.Id.Should().Be(originalToken.Id);
  }

  private class TestEntity : BaseEntity
  {
    // Test entity for testing FromEntity method
  }
}
